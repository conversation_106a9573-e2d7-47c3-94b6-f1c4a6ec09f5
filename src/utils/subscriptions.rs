use std::collections::HashMap;

use crate::generated::{SubscribeEntriesRequest, SubscribeRequestFilterAccounts};

pub fn create_subscribe_request(accounts: Option<&Vec<String>>) -> SubscribeEntriesRequest {
    if let Some(accounts) = accounts {
        if !accounts.is_empty() {
            return create_accounts_subscribe_request(accounts);
        }
    }

    create_empty_subscribe_request()
}

pub fn create_empty_subscribe_request() -> SubscribeEntriesRequest {
    SubscribeEntriesRequest {
        accounts: HashMap::new(),
        transactions: HashMap::new(),
        slots: HashMap::new(),
        commitment: None,
    }
}

pub fn create_accounts_subscribe_request(accounts: &[String]) -> SubscribeEntriesRequest {
    let mut accounts_map = HashMap::new();

    accounts_map.insert("".to_string(), SubscribeRequestFilterAccounts {
        account: accounts.to_vec(),
        owner: vec![],
        filters: vec![],
        nonempty_txn_signature: None,
    });

    SubscribeEntriesRequest {
        accounts: accounts_map,
        transactions: HashMap::new(),
        slots: HashMap::new(),
        commitment: None,
    }
}
