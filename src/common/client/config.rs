use std::time::Duration;

pub struct ShredstreamClientConfig {
    pub connect_timeout: Duration,
    pub subscribe_timeout: Duration,
    pub filters: ShredstreamClientFilters,
}

pub struct ShredstreamClientFilters {
    pub accounts: Option<Vec<String>>,
}

impl Default for ShredstreamClientConfig {
    fn default() -> Self {
        Self {
            connect_timeout: Duration::from_secs(10),
            subscribe_timeout: Duration::from_secs(30),
            filters: ShredstreamClientFilters::default(),
        }
    }
}

impl Default for ShredstreamClientFilters {
    fn default() -> Self {
        Self { accounts: None }
    }
}
