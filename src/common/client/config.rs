use std::time::Duration;

pub struct ClientConfig {
    pub endpoint: String,
    pub connect_timeout: Duration,
    pub subscribe_timeout: Duration,
    pub accounts: Option<Vec<String>>,
}

impl ClientConfig {
    pub fn new(endpoint: String) -> Self {
        Self {
            endpoint,
            connect_timeout: Duration::from_secs(10),
            subscribe_timeout: Duration::from_secs(30),
            accounts: None,
        }
    }

    pub fn with_accounts(mut self, accounts: Vec<String>) -> Self {
        self.accounts = Some(accounts);
        self
    }

    pub fn with_connect_timeout(mut self, timeout: Duration) -> Self {
        self.connect_timeout = timeout;
        self
    }

    pub fn with_subscribe_timeout(mut self, timeout: Duration) -> Self {
        self.subscribe_timeout = timeout;
        self
    }
}
