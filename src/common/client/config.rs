use std::time::Duration;

pub struct ClientConfig {
    pub endpoint: String,
    pub connect_timeout: Duration,
    pub subscribe_timeout: Duration,
    pub accounts: Option<Vec<String>>,
}

impl Default for ClientConfig {
    fn default() -> Self {
        Self {
            endpoint: String::new(),
            connect_timeout: Duration::from_secs(10),
            subscribe_timeout: Duration::from_secs(30),
            accounts: None,
        }
    }
}
