use anyhow::{Context, Result};
use tonic::{codec::Streaming, transport::Channel};

use crate::{
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    endpoint: String,
    accounts: Option<Vec<String>>,
    client: Option<ShredstreamProxyClient<Channel>>,
}

impl ShredstreamClient {
    pub fn new(endpoint: String, accounts: Option<Vec<String>>) -> Self {
        Self { endpoint, accounts, client: None }
    }

    pub async fn connect(&mut self) -> Result<()> {
        let client = ShredstreamProxyClient::connect(self.endpoint.clone())
            .await
            .context("Failed to connect to shredstream endpoint")?;

        self.client = Some(client);

        Ok(())
    }

    pub async fn subscribe(&mut self) -> Result<Streaming<Entry>> {
        let request = create_subscribe_request(self.accounts.as_ref());
        let client = self.client.as_mut().context("Client not connected. Call connect() first")?;
        let response = client.subscribe_entries(request).await.context("Failed to subscribe to entries")?;

        Ok(response.into_inner())
    }

    pub async fn connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        self.connect().await?;
        self.subscribe().await
    }
}
