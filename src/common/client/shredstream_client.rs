use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::codec::Streaming;

use crate::{
    common::client::config::ShredstreamClientConfig,
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    endpoint: String,
    config: ShredstreamClientConfig,
    client: Option<ShredstreamProxyClient<tonic::transport::Channel>>,
}

impl ShredstreamClient {
    pub fn new(endpoint: String, config: ShredstreamClientConfig) -> Self {
        Self { endpoint, config, client: None }
    }

    pub async fn subscribe(&mut self) -> Result<Streaming<Entry>> {
        if self.client.is_none() {
            self.connect().await?;
        }

        match self.internal_subscribe().await {
            Ok(stream) => Ok(stream),
            Err(e) => {
                self.cleanup_connection();
                Err(e)
            }
        }
    }

    async fn connect(&mut self) -> Result<()> {
        let connect_future = ShredstreamProxyClient::connect(self.endpoint.clone());
        let client = timeout(self.config.connect_timeout, connect_future)
            .await
            .context("Connect timeout exceeded")?
            .context("Failed to connect to shredstream endpoint")?;

        self.client = Some(client);
        Ok(())
    }

    async fn internal_subscribe(&mut self) -> Result<Streaming<Entry>> {
        let request = create_subscribe_request(self.config.filters.accounts.as_ref());
        let client = self.client.as_mut().context("Client not connected")?;
        let subscribe_future = client.subscribe_entries(request);

        let response = timeout(self.config.subscribe_timeout, subscribe_future)
            .await
            .context("Subscribe timeout exceeded")?
            .context("Failed to subscribe to entries")?;

        Ok(response.into_inner())
    }

    fn cleanup_connection(&mut self) {
        self.client = None;
    }
}
