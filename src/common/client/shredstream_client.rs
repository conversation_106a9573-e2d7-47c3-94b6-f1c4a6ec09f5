use std::sync::Arc;

use anyhow::{Context, Result};
use tokio::{sync::Mutex, time::timeout};
use tonic::codec::Streaming;

use crate::{
    common::client::config::ShredstreamClientConfig,
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    endpoint: String,
    config: ShredstreamClientConfig,
    shared_stream: Arc<Mutex<Option<Arc<Mutex<Streaming<Entry>>>>>>,
    connection_lock: Arc<Mutex<()>>,
}

impl ShredstreamClient {
    pub fn new(endpoint: String, config: ShredstreamClientConfig) -> Self {
        Self { endpoint, config, shared_stream: Arc::new(Mutex::new(None)), connection_lock: Arc::new(Mutex::new(())) }
    }

    pub async fn subscribe(&self) -> Result<Arc<Mutex<Streaming<Entry>>>> {
        {
            let stream_guard = self.shared_stream.lock().await;
            if let Some(existing_stream) = &*stream_guard {
                return Ok(existing_stream.clone());
            }
        }

        let _connection_guard = self.connection_lock.lock().await;

        {
            let stream_guard = self.shared_stream.lock().await;
            if let Some(existing_stream) = &*stream_guard {
                return Ok(existing_stream.clone());
            }
        }

        let stream = self.connect_and_subscribe_internal().await?;
        let shared_stream = Arc::new(Mutex::new(stream));

        {
            let mut stream_guard = self.shared_stream.lock().await;
            *stream_guard = Some(shared_stream.clone());
        }

        Ok(shared_stream)
    }

    pub async fn reset(&self) {
        let mut stream_guard = self.shared_stream.lock().await;
        *stream_guard = None;
    }

    async fn connect_and_subscribe_internal(&self) -> Result<Streaming<Entry>> {
        let connect_future = ShredstreamProxyClient::connect(self.endpoint.clone());
        let mut client = timeout(self.config.connect_timeout, connect_future)
            .await
            .context("Connect timeout exceeded")?
            .context("Failed to connect to shredstream endpoint")?;

        let request = create_subscribe_request(self.config.filters.accounts.as_ref());
        let subscribe_future = client.subscribe_entries(request);

        let response = timeout(self.config.subscribe_timeout, subscribe_future)
            .await
            .context("Subscribe timeout exceeded")?
            .context("Failed to subscribe to entries")?;

        Ok(response.into_inner())
    }
}
