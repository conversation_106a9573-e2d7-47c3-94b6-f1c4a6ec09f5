use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::{codec::Streaming, transport::Channel};

use crate::{
    common::client::config::ClientConfig,
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    config: ClientConfig,
    client: Option<ShredstreamProxyClient<Channel>>,
}

impl ShredstreamClient {
    pub fn new(config: ClientConfig) -> Self {
        Self { config, client: None }
    }

    pub fn new_with_endpoint(endpoint: String, accounts: Option<Vec<String>>) -> Self {
        let config = ClientConfig { endpoint, accounts, ..Default::default() };
        Self::new(config)
    }

    pub async fn connect(&mut self) -> Result<()> {
        let connect_future = ShredstreamProxyClient::connect(self.config.endpoint.clone());
        let client = timeout(self.config.connect_timeout, connect_future)
            .await
            .context("Connect timeout exceeded")?
            .context("Failed to connect to shredstream endpoint")?;

        self.client = Some(client);

        Ok(())
    }

    pub async fn subscribe(&mut self) -> Result<Streaming<Entry>> {
        let request = create_subscribe_request(self.config.accounts.as_ref());
        let client = self.client.as_mut().context("Client not connected. Call connect() first")?;
        let subscribe_future = client.subscribe_entries(request);
        let response = timeout(self.config.subscribe_timeout, subscribe_future)
            .await
            .context("Subscribe timeout exceeded")?
            .context("Failed to subscribe to entries")?;

        Ok(response.into_inner())
    }

    pub async fn connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        self.connect().await?;
        self.subscribe().await
    }
}
