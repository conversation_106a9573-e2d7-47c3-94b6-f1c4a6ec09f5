use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::codec::Streaming;

use crate::{
    common::client::config::ShredstreamClientConfig,
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    endpoint: String,
    config: ShredstreamClientConfig,
}

impl ShredstreamClient {
    pub fn new(endpoint: String, config: ShredstreamClientConfig) -> Self {
        Self { endpoint, config }
    }

    pub async fn connect(&self) -> Result<ShredstreamProxyClient<tonic::transport::Channel>> {
        let connect_future = ShredstreamProxyClient::connect(self.endpoint.clone());
        timeout(self.config.connect_timeout, connect_future)
            .await
            .context("Connect timeout exceeded")?
            .context("Failed to connect to shredstream endpoint")
    }

    pub async fn subscribe(
        &self,
        mut client: ShredstreamProxyClient<tonic::transport::Channel>,
    ) -> Result<Streaming<Entry>> {
        let request = create_subscribe_request(self.config.filters.accounts.as_ref());
        let subscribe_future = client.subscribe_entries(request);

        let response = timeout(self.config.subscribe_timeout, subscribe_future)
            .await
            .context("Subscribe timeout exceeded")?
            .context("Failed to subscribe to entries")?;

        Ok(response.into_inner())
    }

    pub async fn connect_and_subscribe(&self) -> Result<Streaming<Entry>> {
        let client = self.connect().await?;
        self.subscribe(client).await
    }
}
