mod common;
mod config;
mod core;
mod generated;
mod utils;

use core::{config::load_config, logger::Logger};

use common::client::{
    ShredstreamClient,
    config::{ShredstreamClientConfig, ShredstreamClientFilters},
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    rustls::crypto::ring::default_provider().install_default().ok();

    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    tracing::info!("🧪 TESTING REFACTORED SHREDSTREAMCLIENT");
    tracing::info!("📋 Subscription utilities moved to utils/subscriptions.rs");
    tracing::info!("🔧 Code is now more modular and reusable");

    let endpoint = "https://shreds-far-point-1.erpc.global".to_string();
    let test_account = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string();

    tracing::info!("🌐 Testing with endpoint: {}", endpoint);
    tracing::info!("🔍 Testing with account filter: {}", test_account);

    let accounts = Some(vec![test_account]);
    let config = ShredstreamClientConfig {
        connect_timeout: std::time::Duration::from_secs(10),
        subscribe_timeout: std::time::Duration::from_secs(30),
        filters: ShredstreamClientFilters { accounts },
    };
    let client = ShredstreamClient::new(endpoint, config);

    match client.subscribe().await {
        Ok(shared_stream) => {
            tracing::info!("✅ Successfully connected and subscribed to shredstream");

            let mut count = 0;
            let mut stream = shared_stream.lock().await;
            while let Some(entry) = stream.message().await? {
                count += 1;
                tracing::info!(
                    "📦 Received entry #{}: slot={}, data_size={} bytes",
                    count,
                    entry.slot,
                    entry.entries.len()
                );

                if count >= 3 {
                    tracing::info!("🎯 SUCCESS! Received {} entries with refactored client!", count);
                    break;
                }
            }
        }
        Err(e) => {
            tracing::error!("❌ Failed to connect or subscribe: {:?}", e);
        }
    }

    tracing::info!("✅ REFACTORING COMPLETE: Multiple subscription utilities available!");
    tracing::info!("🔧 Code is now more modular and follows project guidelines!");
    tracing::info!("🚀 ShredstreamClient ready for production with clean architecture!");

    Ok(())
}
