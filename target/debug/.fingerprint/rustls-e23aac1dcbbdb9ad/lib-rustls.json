{"rustc": 8210029788606052455, "features": "[\"log\", \"logging\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5073168975548574389, "path": 16207314485680614956, "deps": [[2883436298747778685, "pki_types", false, 3104354472164494222], [3722963349756955755, "once_cell", false, 10388039721222781774], [5986029879202738730, "log", false, 6278052248538945941], [6528079939221783635, "zeroize", false, 14544166050563358734], [7161480121686072451, "build_script_build", false, 3967463162066396002], [17003143334332120809, "subtle", false, 2026859284189535572], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 5010370575278084174]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-e23aac1dcbbdb9ad/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}