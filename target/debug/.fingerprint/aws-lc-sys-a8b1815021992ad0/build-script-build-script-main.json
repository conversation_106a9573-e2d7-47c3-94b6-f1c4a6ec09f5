{"rustc": 8210029788606052455, "features": "[\"prebuilt-nasm\"]", "declared_features": "[\"asan\", \"bindgen\", \"prebuilt-nasm\", \"ssl\"]", "target": 10419965325687163515, "profile": 3033921117576893, "path": 16249248260261140334, "deps": [[3378925969027653845, "cc", false, 10488243559935228666], [7499741813737603141, "cmake", false, 15676498569152839301], [11989259058781683633, "dunce", false, 4591317417579611577], [13866570822711233627, "fs_extra", false, 10711557730801661045]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/aws-lc-sys-a8b1815021992ad0/dep-build-script-build-script-main", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}