{"rustc": 8210029788606052455, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"default\", \"log\", \"logging\", \"prefer-post-quantum\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15030315195695952907, "path": 16207314485680614956, "deps": [[125823485620238427, "aws_lc_rs", false, 2287784952217016216], [2883436298747778685, "pki_types", false, 3542663357850011325], [3722963349756955755, "once_cell", false, 639644047255681322], [5491919304041016563, "ring", false, 18411595466235010705], [5986029879202738730, "log", false, 2388828179101140365], [6528079939221783635, "zeroize", false, 8203118835386685545], [7161480121686072451, "build_script_build", false, 3130713473968001416], [17003143334332120809, "subtle", false, 3489594316465445684], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 2130923366561554738]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-1af1222098adbfc3/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}