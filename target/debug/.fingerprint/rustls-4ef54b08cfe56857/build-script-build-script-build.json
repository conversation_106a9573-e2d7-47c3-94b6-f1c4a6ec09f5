{"rustc": 8210029788606052455, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"default\", \"log\", \"logging\", \"prefer-post-quantum\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 5408242616063297496, "profile": 9568288033221340563, "path": 18301808800914128881, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-4ef54b08cfe56857/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}